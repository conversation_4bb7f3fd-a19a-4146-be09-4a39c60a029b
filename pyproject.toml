[project]
name = "peertube-uploader"
version = "0.1.0"
description = "A powerful command-line tool for batch uploading videos to PeerTube servers"
authors = [
    {name = "Ren<PERSON> Manus", email = "<EMAIL>"},
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.12"
keywords = ["peertube", "video", "upload", "cli", "batch"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Topic :: Internet :: WWW/HTTP",
    "Topic :: Multimedia :: Video",
    "Topic :: System :: Archiving",
    "Topic :: Utilities",
]

dependencies = [
    "requests>=2.25.0",
    "click>=8.0.0",
    "tqdm>=4.60.0",
    "python-magic>=0.4.27",
    "pydantic>=2.0.0",
    "pyyaml>=6.0",
    "colorama>=0.4.4",
]

[project.optional-dependencies]
dev = [
    "pytest>=8.4.1",
    "pytest-cov>=4.0.0",
    "ruff>=0.1.0",
    "mypy>=1.0.0",
    "pre-commit>=2.20.0",
]

[project.urls]
Homepage = "https://github.com/yourusername/peertube-uploader"
Repository = "https://github.com/yourusername/peertube-uploader.git"
Issues = "https://github.com/yourusername/peertube-uploader/issues"
Documentation = "https://github.com/yourusername/peertube-uploader#readme"

[project.scripts]
peertube-uploader = "peertube_uploader.cli:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--verbose",
    "--tb=short",
    "--strict-markers",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
]

[tool.ruff]
line-length = 100
target-version = "py312"

[tool.ruff.lint]
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # pyflakes
    "I",   # isort
    "B",   # flake8-bugbear
    "C4",  # flake8-comprehensions
    "UP",  # pyupgrade
    "N",   # pep8-naming
    "S",   # flake8-bandit
    "T20", # flake8-print
    "SIM", # flake8-simplify
    "ARG", # flake8-unused-arguments
    "PTH", # flake8-use-pathlib
    "RUF", # ruff-specific rules
]
ignore = [
    "S101",  # assert used
    "T201",  # print found
    "S603",  # subprocess call: check for execution of untrusted input
    "S607",  # starting a process with a partial executable path
]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "magic.*",
    "colorama.*",
]
ignore_missing_imports = true

[dependency-groups]
dev = [
    "pytest>=8.4.1",
]
