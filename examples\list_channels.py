#!/usr/bin/env python3
"""
Example of listing channels programmatically.

This example shows how to:
1. Set up authentication
2. Get user channels
3. Display channel information
"""

import logging
from peertube_uploader.auth import PeerTubeAuth, PeerTubeCredentials
from peertube_uploader.uploader import PeerTubeUploader

# Configure logging
logging.basicConfig(level=logging.INFO)

def main():
    """List channels example."""
    # Set up credentials (replace with your actual server details)
    credentials = PeerTubeCredentials(
        server_url="https://your-peertube-server.com",
        username="your-username",
        password="your-password"
    )
    
    try:
        # Initialize authentication
        auth = PeerTubeAuth(credentials)
        
        # Test authentication
        if not auth.test_authentication():
            print("❌ Authentication failed!")
            return
        
        print("✅ Authentication successful!")
        
        # Initialize uploader
        uploader = PeerTubeUploader(auth)
        
        # Get channels
        channels = uploader.get_user_channels()
        
        if not channels:
            print("No channels found for this account.")
            return
        
        print(f"\nFound {len(channels)} channel(s):")
        print("-" * 60)
        
        for channel in channels:
            print(f"ID: {channel['id']}")
            print(f"Name: {channel['name']}")
            print(f"Display Name: {channel.get('displayName', 'N/A')}")
            print(f"Description: {channel.get('description', 'N/A')}")
            print(f"URL: {channel['url']}")
            print("-" * 60)
        
        # Example: Get just the channel IDs
        channel_ids = [channel['id'] for channel in channels]
        print(f"Channel IDs: {channel_ids}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
