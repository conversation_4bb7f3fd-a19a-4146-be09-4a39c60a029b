"""Tests for the channels CLI command."""

import json
import unittest
from unittest.mock import Mock, patch
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from peertube_uploader.cli import cli


class TestChannelsCommand(unittest.TestCase):
    """Test the channels CLI command."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.runner = CliRunner()
        
        # Mock channel data
        self.mock_channels = [
            {
                'id': 1,
                'name': 'main-channel',
                'displayName': 'Main Channel'
            },
            {
                'id': 2,
                'name': 'secondary-channel',
                'displayName': 'Secondary Channel'
            }
        ]
    
    @patch('peertube_uploader.cli.PeerTubeUploader')
    @patch('peertube_uploader.cli.PeerTubeAuth')
    @patch('peertube_uploader.cli.Config')
    def test_channels_table_format(self, mock_config_class, mock_auth_class, mock_uploader_class):
        """Test channels command with table format."""
        # Setup mocks
        mock_config = Mock()
        mock_credentials = Mock()
        mock_credentials.server_url = 'https://test.peertube.com'
        mock_config.get_credentials.return_value = mock_credentials
        mock_config_class.return_value = mock_config
        
        mock_auth = Mock()
        mock_auth.test_authentication.return_value = True
        mock_auth_class.return_value = mock_auth
        
        mock_uploader = Mock()
        mock_uploader.get_user_channels.return_value = self.mock_channels
        mock_uploader_class.return_value = mock_uploader
        
        # Run command
        result = self.runner.invoke(cli, ['channels', '--format', 'table'])
        
        # Verify
        self.assertEqual(result.exit_code, 0)
        self.assertIn('ID', result.output)
        self.assertIn('Name', result.output)
        self.assertIn('Display Name', result.output)
        self.assertIn('main-channel', result.output)
        self.assertIn('secondary-channel', result.output)
        self.assertIn('1', result.output)
        self.assertIn('2', result.output)
    
    @patch('peertube_uploader.cli.PeerTubeUploader')
    @patch('peertube_uploader.cli.PeerTubeAuth')
    @patch('peertube_uploader.cli.Config')
    def test_channels_list_format(self, mock_config_class, mock_auth_class, mock_uploader_class):
        """Test channels command with list format."""
        # Setup mocks
        mock_config = Mock()
        mock_credentials = Mock()
        mock_config.get_credentials.return_value = mock_credentials
        mock_config_class.return_value = mock_config
        
        mock_auth = Mock()
        mock_auth.test_authentication.return_value = True
        mock_auth_class.return_value = mock_auth
        
        mock_uploader = Mock()
        mock_uploader.get_user_channels.return_value = self.mock_channels
        mock_uploader_class.return_value = mock_uploader
        
        # Run command
        result = self.runner.invoke(cli, ['channels', '--format', 'list'])
        
        # Verify
        self.assertEqual(result.exit_code, 0)
        lines = result.output.strip().split('\n')
        self.assertIn('1', lines)
        self.assertIn('2', lines)
    
    @patch('peertube_uploader.cli.PeerTubeUploader')
    @patch('peertube_uploader.cli.PeerTubeAuth')
    @patch('peertube_uploader.cli.Config')
    def test_channels_json_format(self, mock_config_class, mock_auth_class, mock_uploader_class):
        """Test channels command with JSON format."""
        # Setup mocks
        mock_config = Mock()
        mock_credentials = Mock()
        mock_config.get_credentials.return_value = mock_credentials
        mock_config_class.return_value = mock_config
        
        mock_auth = Mock()
        mock_auth.test_authentication.return_value = True
        mock_auth_class.return_value = mock_auth
        
        mock_uploader = Mock()
        mock_uploader.get_user_channels.return_value = self.mock_channels
        mock_uploader_class.return_value = mock_uploader
        
        # Run command
        result = self.runner.invoke(cli, ['channels', '--format', 'json'])
        
        # Verify
        self.assertEqual(result.exit_code, 0)
        
        # Parse JSON output
        try:
            channels_data = json.loads(result.output)
            self.assertEqual(len(channels_data), 2)
            self.assertEqual(channels_data[0]['id'], 1)
            self.assertEqual(channels_data[1]['id'], 2)
        except json.JSONDecodeError:
            self.fail("Output is not valid JSON")
    
    @patch('peertube_uploader.cli.Config')
    def test_channels_no_credentials(self, mock_config_class):
        """Test channels command with no credentials."""
        # Setup mocks
        mock_config = Mock()
        mock_config.get_credentials.return_value = None
        mock_config_class.return_value = mock_config
        
        # Run command
        result = self.runner.invoke(cli, ['channels'])
        
        # Verify
        self.assertEqual(result.exit_code, 1)
        self.assertIn('No credentials found', result.output)
    
    @patch('peertube_uploader.cli.PeerTubeUploader')
    @patch('peertube_uploader.cli.PeerTubeAuth')
    @patch('peertube_uploader.cli.Config')
    def test_channels_no_channels(self, mock_config_class, mock_auth_class, mock_uploader_class):
        """Test channels command when no channels are found."""
        # Setup mocks
        mock_config = Mock()
        mock_credentials = Mock()
        mock_config.get_credentials.return_value = mock_credentials
        mock_config_class.return_value = mock_config
        
        mock_auth = Mock()
        mock_auth.test_authentication.return_value = True
        mock_auth_class.return_value = mock_auth
        
        mock_uploader = Mock()
        mock_uploader.get_user_channels.return_value = []
        mock_uploader_class.return_value = mock_uploader
        
        # Run command
        result = self.runner.invoke(cli, ['channels'])
        
        # Verify
        self.assertEqual(result.exit_code, 0)
        self.assertIn('No channels found', result.output)


if __name__ == "__main__":
    unittest.main()
