"""Video file discovery and validation module."""

import logging
import mimetypes
import os
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Set

import magic
from pydantic import BaseModel, Field


logger = logging.getLogger(__name__)


class VideoMetadata(BaseModel):
    """Video file metadata."""
    
    file_path: Path = Field(..., description="Path to video file")
    filename: str = Field(..., description="Filename without path")
    size_bytes: int = Field(..., description="File size in bytes")
    mime_type: str = Field(..., description="MIME type")
    duration_seconds: Optional[float] = Field(None, description="Duration in seconds")
    width: Optional[int] = Field(None, description="Video width")
    height: Optional[int] = Field(None, description="Video height")
    fps: Optional[float] = Field(None, description="Frames per second")
    bitrate: Optional[int] = Field(None, description="Bitrate in bps")
    codec: Optional[str] = Field(None, description="Video codec")
    
    @property
    def size_mb(self) -> float:
        """File size in megabytes."""
        return self.size_bytes / (1024 * 1024)
    
    @property
    def duration_formatted(self) -> str:
        """Human-readable duration."""
        if not self.duration_seconds:
            return "Unknown"
        
        hours = int(self.duration_seconds // 3600)
        minutes = int((self.duration_seconds % 3600) // 60)
        seconds = int(self.duration_seconds % 60)
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"
    
    @property
    def resolution(self) -> str:
        """Video resolution as string."""
        if self.width and self.height:
            return f"{self.width}x{self.height}"
        return "Unknown"


class VideoScanner:
    """Scans directories for video files and extracts metadata."""
    
    # Supported video file extensions
    VIDEO_EXTENSIONS = {
        '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v',
        '.3gp', '.ogv', '.ts', '.mts', '.m2ts', '.vob', '.mpg', '.mpeg',
        '.divx', '.xvid', '.asf', '.rm', '.rmvb', '.f4v'
    }
    
    # Supported MIME types
    VIDEO_MIME_TYPES = {
        'video/mp4', 'video/avi', 'video/x-msvideo', 'video/quicktime',
        'video/x-ms-wmv', 'video/x-flv', 'video/webm', 'video/3gpp',
        'video/ogg', 'video/mp2t', 'video/mpeg', 'video/x-ms-asf'
    }
    
    def __init__(self, use_ffprobe: bool = True):
        """Initialize video scanner.
        
        Args:
            use_ffprobe: Whether to use ffprobe for metadata extraction
        """
        self.use_ffprobe = use_ffprobe
        self.magic_mime = magic.Magic(mime=True)
        
        # Check if ffprobe is available
        if self.use_ffprobe:
            try:
                subprocess.run(['ffprobe', '-version'], 
                             capture_output=True, check=True)
                logger.info("ffprobe is available for metadata extraction")
            except (subprocess.CalledProcessError, FileNotFoundError):
                logger.warning("ffprobe not found, metadata extraction will be limited")
                self.use_ffprobe = False
    
    def is_video_file(self, file_path: Path) -> bool:
        """Check if file is a video file."""
        # Check extension
        if file_path.suffix.lower() not in self.VIDEO_EXTENSIONS:
            return False
        
        # Check MIME type
        try:
            mime_type = self.magic_mime.from_file(str(file_path))
            return mime_type in self.VIDEO_MIME_TYPES or mime_type.startswith('video/')
        except Exception as e:
            logger.warning(f"Could not determine MIME type for {file_path}: {e}")
            # Fall back to extension check
            return True
    
    def extract_metadata_ffprobe(self, file_path: Path) -> Dict:
        """Extract metadata using ffprobe."""
        cmd = [
            'ffprobe',
            '-v', 'quiet',
            '-print_format', 'json',
            '-show_format',
            '-show_streams',
            str(file_path)
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            import json
            data = json.loads(result.stdout)
            
            # Find video stream
            video_stream = None
            for stream in data.get('streams', []):
                if stream.get('codec_type') == 'video':
                    video_stream = stream
                    break
            
            metadata = {}
            
            # Format information
            format_info = data.get('format', {})
            if 'duration' in format_info:
                metadata['duration_seconds'] = float(format_info['duration'])
            if 'bit_rate' in format_info:
                metadata['bitrate'] = int(format_info['bit_rate'])
            
            # Video stream information
            if video_stream:
                if 'width' in video_stream:
                    metadata['width'] = int(video_stream['width'])
                if 'height' in video_stream:
                    metadata['height'] = int(video_stream['height'])
                if 'codec_name' in video_stream:
                    metadata['codec'] = video_stream['codec_name']
                
                # Calculate FPS
                if 'r_frame_rate' in video_stream:
                    fps_str = video_stream['r_frame_rate']
                    if '/' in fps_str:
                        num, den = fps_str.split('/')
                        if int(den) != 0:
                            metadata['fps'] = float(num) / float(den)
                    else:
                        metadata['fps'] = float(fps_str)
            
            return metadata
            
        except (subprocess.CalledProcessError, json.JSONDecodeError, ValueError) as e:
            logger.warning(f"Failed to extract metadata with ffprobe for {file_path}: {e}")
            return {}
    
    def scan_file(self, file_path: Path) -> Optional[VideoMetadata]:
        """Scan a single video file and extract metadata."""
        if not file_path.exists() or not file_path.is_file():
            logger.warning(f"File does not exist or is not a file: {file_path}")
            return None
        
        if not self.is_video_file(file_path):
            logger.debug(f"Skipping non-video file: {file_path}")
            return None
        
        try:
            # Basic file information
            stat = file_path.stat()
            mime_type = self.magic_mime.from_file(str(file_path))
            
            metadata_dict = {
                'file_path': file_path,
                'filename': file_path.name,
                'size_bytes': stat.st_size,
                'mime_type': mime_type
            }
            
            # Extract additional metadata with ffprobe
            if self.use_ffprobe:
                ffprobe_data = self.extract_metadata_ffprobe(file_path)
                metadata_dict.update(ffprobe_data)
            
            return VideoMetadata(**metadata_dict)
            
        except Exception as e:
            logger.error(f"Error scanning file {file_path}: {e}")
            return None
    
    def scan_directory(self, directory: Path, recursive: bool = True) -> List[VideoMetadata]:
        """Scan directory for video files.
        
        Args:
            directory: Directory to scan
            recursive: Whether to scan subdirectories
            
        Returns:
            List of video metadata objects
        """
        if not directory.exists() or not directory.is_dir():
            logger.error(f"Directory does not exist or is not a directory: {directory}")
            return []
        
        video_files = []
        
        try:
            if recursive:
                pattern = "**/*"
            else:
                pattern = "*"
            
            for file_path in directory.glob(pattern):
                if file_path.is_file():
                    metadata = self.scan_file(file_path)
                    if metadata:
                        video_files.append(metadata)
                        logger.info(f"Found video: {metadata.filename} "
                                  f"({metadata.size_mb:.1f}MB, {metadata.duration_formatted})")
            
            logger.info(f"Found {len(video_files)} video files in {directory}")
            return video_files
            
        except Exception as e:
            logger.error(f"Error scanning directory {directory}: {e}")
            return []
    
    def filter_by_size(self, videos: List[VideoMetadata], 
                      min_size_mb: Optional[float] = None,
                      max_size_mb: Optional[float] = None) -> List[VideoMetadata]:
        """Filter videos by file size."""
        filtered = videos
        
        if min_size_mb is not None:
            filtered = [v for v in filtered if v.size_mb >= min_size_mb]
            logger.info(f"Filtered {len(videos) - len(filtered)} videos below {min_size_mb}MB")
        
        if max_size_mb is not None:
            filtered = [v for v in filtered if v.size_mb <= max_size_mb]
            logger.info(f"Filtered {len(videos) - len(filtered)} videos above {max_size_mb}MB")
        
        return filtered
    
    def filter_by_duration(self, videos: List[VideoMetadata],
                          min_duration_seconds: Optional[float] = None,
                          max_duration_seconds: Optional[float] = None) -> List[VideoMetadata]:
        """Filter videos by duration."""
        filtered = videos
        
        if min_duration_seconds is not None:
            filtered = [v for v in filtered 
                       if v.duration_seconds and v.duration_seconds >= min_duration_seconds]
            logger.info(f"Filtered videos below {min_duration_seconds} seconds")
        
        if max_duration_seconds is not None:
            filtered = [v for v in filtered 
                       if v.duration_seconds and v.duration_seconds <= max_duration_seconds]
            logger.info(f"Filtered videos above {max_duration_seconds} seconds")
        
        return filtered
