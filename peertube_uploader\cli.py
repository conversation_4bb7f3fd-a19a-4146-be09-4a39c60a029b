"""Command-line interface for PeerTube Uploader."""

import logging
import sys
from pathlib import Path
from typing import List, Optional

import click
import colorama
from colorama import Fore, Style

from .auth import PeerTubeAuth, PeerTubeCredentials
from .config import Config
from .uploader import PeerTubeUploader, UploadOptions
from .video_scanner import VideoScanner
from .logging_config import setup_logging


# Initialize colorama for cross-platform colored output
colorama.init()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('peertube_uploader.log')
    ]
)

logger = logging.getLogger(__name__)


def print_success(message: str):
    """Print success message in green."""
    click.echo(f"{Fore.GREEN}✓ {message}{Style.RESET_ALL}")


def print_error(message: str):
    """Print error message in red."""
    click.echo(f"{Fore.RED}✗ {message}{Style.RESET_ALL}")


def print_warning(message: str):
    """Print warning message in yellow."""
    click.echo(f"{Fore.YELLOW}⚠ {message}{Style.RESET_ALL}")


def print_info(message: str):
    """Print info message in blue."""
    click.echo(f"{Fore.BLUE}ℹ {message}{Style.RESET_ALL}")


@click.group()
@click.option('--config', '-c', type=click.Path(), help='Configuration file path')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
@click.pass_context
def cli(ctx, config, verbose):
    """PeerTube Uploader - Upload videos to PeerTube servers."""
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Load configuration
    config_path = Path(config) if config else None
    ctx.obj = {'config': Config(config_path)}


@cli.command()
@click.option('--server', '-s', required=True, help='PeerTube server URL')
@click.option('--username', '-u', required=True, help='Username')
@click.option('--password', '-p', required=True, help='Password')
@click.pass_context
def auth(ctx, server, username, password):
    """Test authentication with PeerTube server."""
    print_info(f"Testing authentication with {server}...")
    
    try:
        credentials = PeerTubeCredentials(
            server_url=server,
            username=username,
            password=password
        )
        
        auth_handler = PeerTubeAuth(credentials)
        
        if auth_handler.test_authentication():
            print_success("Authentication successful!")
            
            # Save credentials to config
            config = ctx.obj['config']
            config.set_credentials(credentials)
            config.save()
            print_info(f"Credentials saved to {config.config_path}")
        else:
            print_error("Authentication failed!")
            sys.exit(1)
            
    except Exception as e:
        print_error(f"Authentication error: {e}")
        sys.exit(1)


@cli.command()
@click.argument('directory', type=click.Path(exists=True, file_okay=False, dir_okay=True))
@click.option('--server', '-s', help='PeerTube server URL')
@click.option('--username', '-u', help='Username')
@click.option('--password', '-p', help='Password')
@click.option('--recursive', '-r', is_flag=True, default=True, help='Scan subdirectories')
@click.option('--category', type=int, help='Video category ID')
@click.option('--licence', type=int, help='Video licence ID')
@click.option('--language', help='Video language code')
@click.option('--privacy', type=click.Choice(['1', '2', '3']), default='1', 
              help='Privacy (1=Public, 2=Unlisted, 3=Private)')
@click.option('--tags', help='Comma-separated tags')
@click.option('--channel-id', type=int, help='Channel ID')
@click.option('--description', help='Video description template')
@click.option('--nsfw', is_flag=True, help='Mark videos as NSFW')
@click.option('--no-comments', is_flag=True, help='Disable comments')
@click.option('--no-download', is_flag=True, help='Disable download')
@click.option('--no-wait-transcoding', is_flag=True, help='Don\'t wait for transcoding')
@click.option('--resumable/--no-resumable', default=True, help='Use resumable upload')
@click.option('--dry-run', is_flag=True, help='Show what would be uploaded without uploading')
@click.option('--min-size', type=float, help='Minimum file size in MB')
@click.option('--max-size', type=float, help='Maximum file size in MB')
@click.option('--min-duration', type=float, help='Minimum duration in seconds')
@click.option('--max-duration', type=float, help='Maximum duration in seconds')
@click.pass_context
def upload(ctx, directory, server, username, password, recursive, category, licence, 
          language, privacy, tags, channel_id, description, nsfw, no_comments, 
          no_download, no_wait_transcoding, resumable, dry_run, min_size, max_size,
          min_duration, max_duration):
    """Upload videos from a directory to PeerTube."""
    
    config = ctx.obj['config']
    
    # Get credentials
    if server and username and password:
        credentials = PeerTubeCredentials(
            server_url=server,
            username=username,
            password=password
        )
    else:
        credentials = config.get_credentials()
        if not credentials:
            print_error("No credentials found. Use 'auth' command first or provide --server, --username, --password")
            sys.exit(1)
    
    try:
        # Initialize components
        print_info("Initializing...")
        auth_handler = PeerTubeAuth(credentials)
        uploader = PeerTubeUploader(auth_handler)
        scanner = VideoScanner()
        
        # Test authentication
        if not auth_handler.test_authentication():
            print_error("Authentication failed!")
            sys.exit(1)
        
        # Scan for videos
        print_info(f"Scanning {directory} for videos...")
        video_files = scanner.scan_directory(Path(directory), recursive=recursive)
        
        if not video_files:
            print_warning("No video files found!")
            return
        
        # Apply filters
        if min_size or max_size:
            video_files = scanner.filter_by_size(video_files, min_size, max_size)
        
        if min_duration or max_duration:
            video_files = scanner.filter_by_duration(video_files, min_duration, max_duration)
        
        if not video_files:
            print_warning("No videos match the specified filters!")
            return
        
        print_info(f"Found {len(video_files)} videos to upload")
        
        # Prepare upload options
        upload_options = UploadOptions(
            category=category,
            licence=licence,
            language=language,
            privacy=int(privacy),
            tags=tags.split(',') if tags else None,
            channel_id=channel_id,
            description=description,
            nsfw=nsfw,
            comments_enabled=not no_comments,
            download_enabled=not no_download,
            wait_transcoding=not no_wait_transcoding
        )
        
        if dry_run:
            print_info("DRY RUN - Videos that would be uploaded:")
            for video in video_files:
                print(f"  • {video.filename} ({video.size_mb:.1f}MB, {video.duration_formatted})")
            return
        
        # Upload videos
        successful_uploads = 0
        failed_uploads = 0
        
        for i, video in enumerate(video_files, 1):
            print_info(f"Uploading {i}/{len(video_files)}: {video.filename}")
            
            try:
                # Use filename as title if not specified
                video_options = upload_options.copy()
                if not video_options.name:
                    video_options.name = video.file_path.stem
                
                result = uploader.upload_video(video, video_options, use_resumable=resumable)
                print_success(f"Successfully uploaded: {video.filename}")
                successful_uploads += 1
                
            except Exception as e:
                print_error(f"Failed to upload {video.filename}: {e}")
                failed_uploads += 1
                continue
        
        # Summary
        print_info(f"\nUpload Summary:")
        print_success(f"Successful: {successful_uploads}")
        if failed_uploads > 0:
            print_error(f"Failed: {failed_uploads}")
        
    except Exception as e:
        print_error(f"Upload error: {e}")
        sys.exit(1)


@cli.command()
@click.pass_context
def info(ctx):
    """Show server information and available options."""
    config = ctx.obj['config']
    credentials = config.get_credentials()
    
    if not credentials:
        print_error("No credentials found. Use 'auth' command first.")
        sys.exit(1)
    
    try:
        auth_handler = PeerTubeAuth(credentials)
        uploader = PeerTubeUploader(auth_handler)
        
        if not auth_handler.test_authentication():
            print_error("Authentication failed!")
            sys.exit(1)
        
        print_info(f"Server: {credentials.server_url}")
        
        # Get categories
        print_info("\nAvailable Categories:")
        categories = uploader.get_upload_categories()
        for cat_id, cat_name in categories.items():
            print(f"  {cat_id}: {cat_name}")
        
        # Get licences
        print_info("\nAvailable Licences:")
        licences = uploader.get_upload_licences()
        for lic_id, lic_name in licences.items():
            print(f"  {lic_id}: {lic_name}")
        
        # Get channels
        print_info("\nYour Channels:")
        channels = uploader.get_user_channels()
        for channel in channels:
            print(f"  {channel['id']}: {channel['name']}")
        
    except Exception as e:
        print_error(f"Error getting server info: {e}")
        sys.exit(1)


@cli.command()
@click.option('--format', '-f', type=click.Choice(['table', 'list', 'json']), default='table',
              help='Output format (table, list, or json)')
@click.pass_context
def channels(ctx, format):
    """List available channels and their IDs."""
    config = ctx.obj['config']
    credentials = config.get_credentials()

    if not credentials:
        print_error("No credentials found. Use 'auth' command first.")
        sys.exit(1)

    try:
        auth_handler = PeerTubeAuth(credentials)
        uploader = PeerTubeUploader(auth_handler)

        if not auth_handler.test_authentication():
            print_error("Authentication failed!")
            sys.exit(1)

        # Get channels
        channels_data = uploader.get_user_channels()

        if not channels_data:
            print_warning("No channels found for this account.")
            return

        if format == 'json':
            import json
            print(json.dumps(channels_data, indent=2))
        elif format == 'list':
            for channel in channels_data:
                print(f"{channel['id']}")
        else:  # table format (default)
            print_info(f"Channels for {credentials.server_url}:")
            print()
            print(f"{'ID':<8} {'Name':<30} {'Display Name'}")
            print("-" * 60)
            for channel in channels_data:
                display_name = channel.get('displayName', '')
                print(f"{channel['id']:<8} {channel['name']:<30} {display_name}")

    except Exception as e:
        print_error(f"Error getting channels: {e}")
        sys.exit(1)


@cli.command()
@click.argument('directory', type=click.Path(exists=True, file_okay=False, dir_okay=True))
@click.option('--recursive', '-r', is_flag=True, default=True, help='Scan subdirectories')
@click.option('--min-size', type=float, help='Minimum file size in MB')
@click.option('--max-size', type=float, help='Maximum file size in MB')
@click.option('--min-duration', type=float, help='Minimum duration in seconds')
@click.option('--max-duration', type=float, help='Maximum duration in seconds')
def scan(directory, recursive, min_size, max_size, min_duration, max_duration):
    """Scan directory for video files without uploading."""
    
    print_info(f"Scanning {directory} for videos...")
    scanner = VideoScanner()
    video_files = scanner.scan_directory(Path(directory), recursive=recursive)
    
    if not video_files:
        print_warning("No video files found!")
        return
    
    # Apply filters
    if min_size or max_size:
        video_files = scanner.filter_by_size(video_files, min_size, max_size)
    
    if min_duration or max_duration:
        video_files = scanner.filter_by_duration(video_files, min_duration, max_duration)
    
    if not video_files:
        print_warning("No videos match the specified filters!")
        return
    
    print_info(f"Found {len(video_files)} video files:")
    
    total_size = 0
    total_duration = 0
    
    for video in video_files:
        print(f"  • {video.filename}")
        print(f"    Size: {video.size_mb:.1f}MB")
        print(f"    Duration: {video.duration_formatted}")
        if video.resolution != "Unknown":
            print(f"    Resolution: {video.resolution}")
        print()
        
        total_size += video.size_mb
        if video.duration_seconds:
            total_duration += video.duration_seconds
    
    print_info(f"Total size: {total_size:.1f}MB")
    if total_duration > 0:
        hours = int(total_duration // 3600)
        minutes = int((total_duration % 3600) // 60)
        seconds = int(total_duration % 60)
        print_info(f"Total duration: {hours:02d}:{minutes:02d}:{seconds:02d}")


def main():
    """Main entry point."""
    cli()
