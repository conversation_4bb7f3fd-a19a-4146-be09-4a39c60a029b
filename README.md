# PeerTube Uploader

A powerful command-line tool for batch uploading videos to PeerTube servers with support for resumable uploads, progress tracking, and robust error handling.

## Features

- **Batch Upload**: Upload multiple videos from a directory with a single command
- **Resumable Uploads**: Support for PeerTube's resumable upload API for large files
- **Progress Tracking**: Real-time progress bars and detailed upload statistics
- **Robust Error Handling**: Automatic retry logic for network issues and server errors
- **Flexible Configuration**: Store server credentials and default settings in configuration files
- **Video Filtering**: Filter videos by size, duration, and format before uploading
- **Metadata Support**: Extract and set video metadata including title, description, tags, and categories
- **Cross-Platform**: Works on Windows, macOS, and Linux

## Installation

### From Source

```bash
git clone https://github.com/yourusername/peertube-uploader.git
cd peertube-uploader
pip install -e .
```

### Using pip (when published)

```bash
pip install peertube-uploader
```

## Quick Start

1. **Authenticate with your PeerTube server:**

```bash
peertube-uploader auth --server https://your-peertube-server.com --username your_username --password your_password
```

2. **Upload videos from a directory:**

```bash
peertube-uploader upload /path/to/your/videos
```

3. **Upload with custom settings:**

```bash
peertube-uploader upload /path/to/videos --category 1 --privacy 1 --tags "tutorial,demo" --description "My video description"
```

## Usage

### Authentication

Before uploading videos, you need to authenticate with your PeerTube server:

```bash
peertube-uploader auth --server https://peertube.example.com --username myuser --password mypass
```

This will save your credentials securely for future use.

### Uploading Videos

#### Basic Upload

Upload all videos from a directory:

```bash
peertube-uploader upload /path/to/videos
```

#### Upload with Options

```bash
peertube-uploader upload /path/to/videos \
  --category 1 \
  --licence 1 \
  --language en \
  --privacy 1 \
  --tags "tutorial,demo,example" \
  --channel-id 123 \
  --description "Default description for all videos" \
  --nsfw \
  --no-comments \
  --recursive
```

#### Filtering Videos

Filter videos by size and duration:

```bash
peertube-uploader upload /path/to/videos \
  --min-size 10 \
  --max-size 500 \
  --min-duration 60 \
  --max-duration 3600
```

#### Dry Run

See what would be uploaded without actually uploading:

```bash
peertube-uploader upload /path/to/videos --dry-run
```

### Scanning Videos

Scan a directory to see what videos would be found:

```bash
peertube-uploader scan /path/to/videos --recursive
```

### Server Information

Get information about your PeerTube server:

```bash
peertube-uploader info
```

This shows available categories, licences, and your channels.

## Configuration

The tool uses a configuration file to store settings. The default location is:

- **Windows**: `%LOCALAPPDATA%\PeerTubeUploader\config.yaml`
- **macOS/Linux**: `~/.config/peertube-uploader/config.yaml`

### Example Configuration

```yaml
credentials:
  server_url: "https://peertube.example.com"
  username: "myuser"
  password: "mypassword"

upload_defaults:
  category: 1
  licence: 1
  language: "en"
  privacy: 1
  tags: ["default", "tag"]
  nsfw: false
  comments_enabled: true
  download_enabled: true
  wait_transcoding: true
  use_resumable: true
  chunk_size_mb: 1.0

scan_defaults:
  recursive: true
  min_size_mb: 1.0
  max_size_mb: 1000.0

log_level: "INFO"
log_file: "peertube_uploader.log"
```

## Command Reference

### Global Options

- `--config, -c`: Path to configuration file
- `--verbose, -v`: Enable verbose logging

### Commands

#### `auth`

Authenticate with a PeerTube server.

**Options:**
- `--server, -s`: PeerTube server URL (required)
- `--username, -u`: Username (required)
- `--password, -p`: Password (required)

#### `upload`

Upload videos from a directory.

**Arguments:**
- `directory`: Path to directory containing videos

**Options:**
- `--server, -s`: Override server URL
- `--username, -u`: Override username
- `--password, -p`: Override password
- `--recursive, -r`: Scan subdirectories (default: true)
- `--category`: Video category ID
- `--licence`: Video licence ID
- `--language`: Video language code
- `--privacy`: Privacy setting (1=Public, 2=Unlisted, 3=Private)
- `--tags`: Comma-separated tags
- `--channel-id`: Channel ID
- `--description`: Video description template
- `--nsfw`: Mark videos as NSFW
- `--no-comments`: Disable comments
- `--no-download`: Disable download
- `--no-wait-transcoding`: Don't wait for transcoding
- `--resumable/--no-resumable`: Use resumable upload (default: true)
- `--dry-run`: Show what would be uploaded without uploading
- `--min-size`: Minimum file size in MB
- `--max-size`: Maximum file size in MB
- `--min-duration`: Minimum duration in seconds
- `--max-duration`: Maximum duration in seconds

#### `scan`

Scan directory for video files.

**Arguments:**
- `directory`: Path to directory to scan

**Options:**
- `--recursive, -r`: Scan subdirectories (default: true)
- `--min-size`: Minimum file size in MB
- `--max-size`: Maximum file size in MB
- `--min-duration`: Minimum duration in seconds
- `--max-duration`: Maximum duration in seconds

#### `info`

Show server information and available options.

#### `channels`

List available channels and their IDs.

**Options:**
- `--format, -f`: Output format (table, list, or json) (default: table)

**Examples:**
```bash
# Show channels in table format (default)
peertube-uploader channels

# Get just the channel IDs
peertube-uploader channels --format list

# Get full channel data as JSON
peertube-uploader channels --format json
```

## Supported Video Formats

The tool automatically detects video files with the following extensions:

- MP4 (`.mp4`)
- AVI (`.avi`)
- MOV (`.mov`)
- MKV (`.mkv`)
- WebM (`.webm`)
- FLV (`.flv`)
- WMV (`.wmv`)
- M4V (`.m4v`)
- 3GP (`.3gp`)
- OGV (`.ogv`)

## Error Handling

The tool includes robust error handling with automatic retry logic:

- **Network errors**: Automatically retried with exponential backoff
- **Server errors**: Retried with configurable delays
- **Rate limiting**: Respects server rate limits and retry-after headers
- **Authentication errors**: Not retried (requires user intervention)
- **Validation errors**: Not retried (requires fixing input)

## Logging

Logs are written to both console and file:

- **Console**: Colored output with configurable verbosity
- **File**: Detailed logs with rotation (default: `peertube_uploader.log`)

Log levels: `DEBUG`, `INFO`, `WARNING`, `ERROR`, `CRITICAL`

## Examples

### Upload Tutorial Videos

```bash
# Upload all videos from tutorials directory with specific settings
peertube-uploader upload ~/Videos/tutorials \
  --category 15 \
  --licence 1 \
  --language en \
  --privacy 1 \
  --tags "tutorial,education" \
  --description "Educational tutorial video"
```

### Upload Large Files with Resumable Upload

```bash
# Upload large files using resumable upload
peertube-uploader upload ~/Videos/large-files \
  --resumable \
  --min-size 100
```

### Batch Upload with Filtering

```bash
# Upload only videos between 5-30 minutes
peertube-uploader upload ~/Videos \
  --min-duration 300 \
  --max-duration 1800 \
  --recursive
```

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Check server URL, username, and password
   - Ensure your account has upload permissions

2. **Upload Fails**
   - Check network connection
   - Verify video file is not corrupted
   - Check server disk space and upload limits

3. **Permission Denied**
   - Ensure your account has permission to upload to the specified channel
   - Check if the server allows uploads from your account

### Debug Mode

Enable verbose logging for troubleshooting:

```bash
peertube-uploader --verbose upload /path/to/videos
```

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

If you encounter any issues or have questions, please open an issue on GitHub.
