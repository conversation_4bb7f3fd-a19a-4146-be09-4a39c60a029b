"""Tests for video scanner functionality."""

import tempfile
import unittest
from pathlib import Path


from peertube_uploader.video_scanner import VideoMetadata, VideoScanner


class TestVideoMetadata(unittest.TestCase):
    """Test VideoMetadata class."""
    
    def test_video_metadata_creation(self):
        """Test creating VideoMetadata object."""
        file_path = Path("/test/video.mp4")
        
        metadata = VideoMetadata(
            file_path=file_path,
            filename="video.mp4",
            size_bytes=1024 * 1024 * 10,  # 10MB
            mime_type="video/mp4",
            duration_seconds=120.5,
            width=1920,
            height=1080,
            codec="h264"
        )
        
        self.assertEqual(metadata.file_path, file_path)
        self.assertEqual(metadata.filename, "video.mp4")
        self.assertEqual(metadata.size_bytes, 1024 * 1024 * 10)
        self.assertEqual(metadata.size_mb, 10.0)
        self.assertEqual(metadata.duration_seconds, 120.5)
        self.assertEqual(metadata.duration_formatted, "02:00")
        self.assertEqual(metadata.resolution, "1920x1080")
    
    def test_duration_formatting(self):
        """Test duration formatting."""
        # Test various durations
        test_cases = [
            (0, "00:00"),
            (30, "00:30"),
            (60, "01:00"),
            (90, "01:30"),
            (3600, "1:00:00"),
            (3661, "1:01:01"),
            (7200, "2:00:00")
        ]
        
        for seconds, expected in test_cases:
            metadata = VideoMetadata(
                file_path=Path("/test/video.mp4"),
                filename="video.mp4",
                size_bytes=1024,
                mime_type="video/mp4",
                duration_seconds=seconds
            )
            self.assertEqual(metadata.duration_formatted, expected)
    
    def test_duration_formatting_none(self):
        """Test duration formatting when duration is None."""
        metadata = VideoMetadata(
            file_path=Path("/test/video.mp4"),
            filename="video.mp4",
            size_bytes=1024,
            mime_type="video/mp4"
        )
        self.assertEqual(metadata.duration_formatted, "Unknown")


class TestVideoScanner(unittest.TestCase):
    """Test VideoScanner class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.scanner = VideoScanner()
    
    def test_is_video_file(self):
        """Test video file detection."""
        # Create temporary files to test with
        import tempfile

        # Test video files
        video_extensions = [".mp4", ".avi", ".mov", ".mkv", ".webm", ".flv", ".wmv", ".m4v", ".3gp", ".ogv"]

        for ext in video_extensions:
            with tempfile.NamedTemporaryFile(suffix=ext, delete=False) as temp_file:
                temp_path = Path(temp_file.name)
                temp_file.write(b"fake video data")

            try:
                with self.subTest(extension=ext):
                    self.assertTrue(self.scanner.is_video_file(temp_path))
            finally:
                temp_path.unlink()

        # Test non-video files
        non_video_extensions = [".txt", ".jpg", ".mp3", ".zip", ".py"]

        for ext in non_video_extensions:
            with tempfile.NamedTemporaryFile(suffix=ext, delete=False) as temp_file:
                temp_path = Path(temp_file.name)
                temp_file.write(b"fake data")

            try:
                with self.subTest(extension=ext):
                    self.assertFalse(self.scanner.is_video_file(temp_path))
            finally:
                temp_path.unlink()
    
    def test_filter_by_size(self):
        """Test filtering videos by size."""
        # Create test videos with different sizes
        videos = [
            VideoMetadata(
                file_path=Path("small.mp4"),
                filename="small.mp4",
                size_bytes=1024 * 1024,  # 1MB
                mime_type="video/mp4"
            ),
            VideoMetadata(
                file_path=Path("medium.mp4"),
                filename="medium.mp4",
                size_bytes=10 * 1024 * 1024,  # 10MB
                mime_type="video/mp4"
            ),
            VideoMetadata(
                file_path=Path("large.mp4"),
                filename="large.mp4",
                size_bytes=100 * 1024 * 1024,  # 100MB
                mime_type="video/mp4"
            )
        ]
        
        # Test minimum size filter
        filtered = self.scanner.filter_by_size(videos, min_size_mb=5.0)
        self.assertEqual(len(filtered), 2)
        self.assertNotIn(videos[0], filtered)  # small.mp4 should be filtered out
        
        # Test maximum size filter
        filtered = self.scanner.filter_by_size(videos, max_size_mb=50.0)
        self.assertEqual(len(filtered), 2)
        self.assertNotIn(videos[2], filtered)  # large.mp4 should be filtered out
        
        # Test both filters
        filtered = self.scanner.filter_by_size(videos, min_size_mb=5.0, max_size_mb=50.0)
        self.assertEqual(len(filtered), 1)
        self.assertEqual(filtered[0], videos[1])  # Only medium.mp4 should remain
    
    def test_filter_by_duration(self):
        """Test filtering videos by duration."""
        # Create test videos with different durations
        videos = [
            VideoMetadata(
                file_path=Path("short.mp4"),
                filename="short.mp4",
                size_bytes=1024,
                mime_type="video/mp4",
                duration_seconds=30
            ),
            VideoMetadata(
                file_path=Path("medium.mp4"),
                filename="medium.mp4",
                size_bytes=1024,
                mime_type="video/mp4",
                duration_seconds=300  # 5 minutes
            ),
            VideoMetadata(
                file_path=Path("long.mp4"),
                filename="long.mp4",
                size_bytes=1024,
                mime_type="video/mp4",
                duration_seconds=3600  # 1 hour
            ),
            VideoMetadata(
                file_path=Path("unknown.mp4"),
                filename="unknown.mp4",
                size_bytes=1024,
                mime_type="video/mp4"
                # No duration
            )
        ]
        
        # Test minimum duration filter
        filtered = self.scanner.filter_by_duration(videos, min_duration_seconds=60)
        self.assertEqual(len(filtered), 2)
        self.assertNotIn(videos[0], filtered)  # short.mp4 should be filtered out
        self.assertNotIn(videos[3], filtered)  # unknown.mp4 should be filtered out
        
        # Test maximum duration filter
        filtered = self.scanner.filter_by_duration(videos, max_duration_seconds=1800)
        self.assertEqual(len(filtered), 2)
        self.assertNotIn(videos[2], filtered)  # long.mp4 should be filtered out
        self.assertNotIn(videos[3], filtered)  # unknown.mp4 should be filtered out
        
        # Test both filters
        filtered = self.scanner.filter_by_duration(videos, min_duration_seconds=60, max_duration_seconds=1800)
        self.assertEqual(len(filtered), 1)
        self.assertEqual(filtered[0], videos[1])  # Only medium.mp4 should remain
    
    def test_scan_file_success(self):
        """Test successful file scanning."""
        # Create a temporary file
        with tempfile.NamedTemporaryFile(suffix=".mp4", delete=False) as temp_file:
            temp_path = Path(temp_file.name)
            temp_file.write(b"fake video data")

        try:
            scanner = VideoScanner(use_ffprobe=False)
            metadata = scanner.scan_file(temp_path)

            self.assertIsNotNone(metadata)
            self.assertEqual(metadata.file_path, temp_path)
            self.assertEqual(metadata.filename, temp_path.name)
            self.assertEqual(metadata.mime_type, "video/unknown")  # Default since we removed MIME checking
            self.assertGreater(metadata.size_bytes, 0)

        finally:
            # Clean up
            temp_path.unlink()
    
    def test_scan_file_nonexistent(self):
        """Test scanning non-existent file."""
        result = self.scanner.scan_file(Path("/nonexistent/file.mp4"))
        self.assertIsNone(result)
    
    def test_scan_file_non_video(self):
        """Test scanning non-video file."""
        # Create a temporary text file
        with tempfile.NamedTemporaryFile(suffix=".txt", delete=False) as temp_file:
            temp_path = Path(temp_file.name)
            temp_file.write(b"not a video")
        
        try:
            result = self.scanner.scan_file(temp_path)
            self.assertIsNone(result)
        finally:
            temp_path.unlink()
    
    def test_scan_directory(self):
        """Test directory scanning."""
        # Create temporary directory with video files
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)

            # Create some test files
            video_files = ["video1.mp4", "video2.avi", "video3.mov"]
            non_video_files = ["readme.txt", "image.jpg"]

            for filename in video_files + non_video_files:
                file_path = temp_path / filename
                file_path.write_bytes(b"test content")

            # Create subdirectory with video
            subdir = temp_path / "subdir"
            subdir.mkdir()
            (subdir / "subvideo.mp4").write_bytes(b"test content")

            scanner = VideoScanner(use_ffprobe=False)

            # Test recursive scan
            videos = scanner.scan_directory(temp_path, recursive=True)
            self.assertEqual(len(videos), 4)  # 3 in root + 1 in subdir

            # Test non-recursive scan
            videos = scanner.scan_directory(temp_path, recursive=False)
            self.assertEqual(len(videos), 3)  # Only in root directory


if __name__ == "__main__":
    unittest.main()
