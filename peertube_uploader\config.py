"""Configuration management for PeerTube Uploader."""

import logging
import os
from pathlib import Path
from typing import Dict, Optional

import yaml
from pydantic import BaseModel, Field

from .auth import PeerTubeCredentials


logger = logging.getLogger(__name__)


class UploadDefaults(BaseModel):
    """Default upload settings."""
    
    category: Optional[int] = Field(None, description="Default video category ID")
    licence: Optional[int] = Field(None, description="Default video licence ID")
    language: Optional[str] = Field(None, description="Default video language code")
    privacy: int = Field(1, description="Default privacy setting")
    tags: Optional[list] = Field(None, description="Default video tags")
    channel_id: Optional[int] = Field(None, description="Default channel ID")
    description_template: Optional[str] = Field(None, description="Default description template")
    nsfw: bool = Field(False, description="Default NSFW flag")
    comments_enabled: bool = Field(True, description="Default comments setting")
    download_enabled: bool = Field(True, description="Default download setting")
    wait_transcoding: bool = Field(True, description="Default transcoding wait setting")
    use_resumable: bool = Field(True, description="Default resumable upload setting")
    chunk_size_mb: float = Field(1.0, description="Default chunk size in MB")


class ScanDefaults(BaseModel):
    """Default scan settings."""
    
    recursive: bool = Field(True, description="Default recursive scan setting")
    min_size_mb: Optional[float] = Field(None, description="Default minimum file size in MB")
    max_size_mb: Optional[float] = Field(None, description="Default maximum file size in MB")
    min_duration_seconds: Optional[float] = Field(None, description="Default minimum duration")
    max_duration_seconds: Optional[float] = Field(None, description="Default maximum duration")


class ConfigData(BaseModel):
    """Configuration data structure."""
    
    credentials: Optional[PeerTubeCredentials] = Field(None, description="Server credentials")
    upload_defaults: UploadDefaults = Field(default_factory=UploadDefaults, description="Upload defaults")
    scan_defaults: ScanDefaults = Field(default_factory=ScanDefaults, description="Scan defaults")
    log_level: str = Field("INFO", description="Logging level")
    log_file: str = Field("peertube_uploader.log", description="Log file path")


class Config:
    """Configuration manager."""
    
    def __init__(self, config_path: Optional[Path] = None):
        """Initialize configuration manager.
        
        Args:
            config_path: Path to configuration file. If None, uses default location.
        """
        if config_path:
            self.config_path = Path(config_path)
        else:
            # Default config locations
            if os.name == 'nt':  # Windows
                config_dir = Path.home() / 'AppData' / 'Local' / 'PeerTubeUploader'
            else:  # Unix-like
                config_dir = Path.home() / '.config' / 'peertube-uploader'
            
            config_dir.mkdir(parents=True, exist_ok=True)
            self.config_path = config_dir / 'config.yaml'
        
        self.data = self._load_config()
    
    def _load_config(self) -> ConfigData:
        """Load configuration from file."""
        if not self.config_path.exists():
            logger.info(f"Config file not found at {self.config_path}, using defaults")
            return ConfigData()
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config_dict = yaml.safe_load(f) or {}
            
            # Handle credentials separately to avoid password in logs
            if 'credentials' in config_dict:
                creds_dict = config_dict['credentials']
                # Don't log password
                safe_creds = {k: v for k, v in creds_dict.items() if k != 'password'}
                logger.info(f"Loaded credentials for server: {safe_creds.get('server_url', 'unknown')}")
            
            return ConfigData(**config_dict)
            
        except Exception as e:
            logger.error(f"Error loading config from {self.config_path}: {e}")
            logger.info("Using default configuration")
            return ConfigData()
    
    def save(self) -> bool:
        """Save configuration to file."""
        try:
            # Ensure directory exists
            self.config_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Convert to dict for YAML serialization
            config_dict = self.data.model_dump(exclude_none=True)
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_dict, f, default_flow_style=False, indent=2)
            
            logger.info(f"Configuration saved to {self.config_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving config to {self.config_path}: {e}")
            return False
    
    def get_credentials(self) -> Optional[PeerTubeCredentials]:
        """Get stored credentials."""
        return self.data.credentials
    
    def set_credentials(self, credentials: PeerTubeCredentials):
        """Set credentials."""
        self.data.credentials = credentials
        logger.info(f"Credentials set for server: {credentials.server_url}")
    
    def get_upload_defaults(self) -> UploadDefaults:
        """Get upload defaults."""
        return self.data.upload_defaults
    
    def set_upload_defaults(self, defaults: UploadDefaults):
        """Set upload defaults."""
        self.data.upload_defaults = defaults
        logger.info("Upload defaults updated")
    
    def get_scan_defaults(self) -> ScanDefaults:
        """Get scan defaults."""
        return self.data.scan_defaults
    
    def set_scan_defaults(self, defaults: ScanDefaults):
        """Set scan defaults."""
        self.data.scan_defaults = defaults
        logger.info("Scan defaults updated")
    
    def get_log_level(self) -> str:
        """Get log level."""
        return self.data.log_level
    
    def set_log_level(self, level: str):
        """Set log level."""
        self.data.log_level = level.upper()
        logger.info(f"Log level set to {self.data.log_level}")
    
    def get_log_file(self) -> str:
        """Get log file path."""
        return self.data.log_file
    
    def set_log_file(self, path: str):
        """Set log file path."""
        self.data.log_file = path
        logger.info(f"Log file set to {path}")
    
    def reset_to_defaults(self):
        """Reset configuration to defaults."""
        self.data = ConfigData()
        logger.info("Configuration reset to defaults")
    
    def export_config(self, export_path: Path, include_credentials: bool = False) -> bool:
        """Export configuration to a file.
        
        Args:
            export_path: Path to export file
            include_credentials: Whether to include credentials in export
            
        Returns:
            True if export successful
        """
        try:
            config_dict = self.data.model_dump(exclude_none=True)
            
            if not include_credentials and 'credentials' in config_dict:
                # Remove sensitive information
                creds = config_dict['credentials'].copy()
                creds.pop('password', None)
                creds.pop('access_token', None)
                creds.pop('refresh_token', None)
                creds.pop('client_secret', None)
                config_dict['credentials'] = creds
            
            with open(export_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_dict, f, default_flow_style=False, indent=2)
            
            logger.info(f"Configuration exported to {export_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting config to {export_path}: {e}")
            return False
    
    def import_config(self, import_path: Path) -> bool:
        """Import configuration from a file.
        
        Args:
            import_path: Path to import file
            
        Returns:
            True if import successful
        """
        try:
            with open(import_path, 'r', encoding='utf-8') as f:
                config_dict = yaml.safe_load(f) or {}
            
            # Validate and merge with current config
            imported_data = ConfigData(**config_dict)
            
            # Merge with existing data (preserve current credentials if not in import)
            if imported_data.credentials is None and self.data.credentials is not None:
                imported_data.credentials = self.data.credentials
            
            self.data = imported_data
            logger.info(f"Configuration imported from {import_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error importing config from {import_path}: {e}")
            return False
    
    def validate_config(self) -> Dict[str, list]:
        """Validate current configuration.
        
        Returns:
            Dictionary with validation results
        """
        issues = {
            'errors': [],
            'warnings': [],
            'info': []
        }
        
        # Check credentials
        if not self.data.credentials:
            issues['warnings'].append("No credentials configured")
        else:
            creds = self.data.credentials
            if not creds.server_url:
                issues['errors'].append("Server URL is required")
            if not creds.username:
                issues['errors'].append("Username is required")
            if not creds.password:
                issues['errors'].append("Password is required")
        
        # Check upload defaults
        upload_defaults = self.data.upload_defaults
        if upload_defaults.chunk_size_mb <= 0:
            issues['errors'].append("Chunk size must be positive")
        if upload_defaults.chunk_size_mb > 100:
            issues['warnings'].append("Large chunk size may cause memory issues")
        
        # Check scan defaults
        scan_defaults = self.data.scan_defaults
        if scan_defaults.min_size_mb and scan_defaults.max_size_mb:
            if scan_defaults.min_size_mb > scan_defaults.max_size_mb:
                issues['errors'].append("Minimum size cannot be larger than maximum size")
        
        if scan_defaults.min_duration_seconds and scan_defaults.max_duration_seconds:
            if scan_defaults.min_duration_seconds > scan_defaults.max_duration_seconds:
                issues['errors'].append("Minimum duration cannot be larger than maximum duration")
        
        # Log level validation
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if self.data.log_level not in valid_levels:
            issues['errors'].append(f"Invalid log level: {self.data.log_level}")
        
        return issues
