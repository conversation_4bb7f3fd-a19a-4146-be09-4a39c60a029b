"""Tests for authentication functionality."""

import unittest
from unittest.mock import Mock, patch

import requests

from peertube_uploader.auth import P<PERSON><PERSON><PERSON><PERSON><PERSON>, PeerTubeCredentials


class TestPeerTubeCredentials(unittest.TestCase):
    """Test PeerTubeCredentials class."""
    
    def test_credentials_creation(self):
        """Test creating credentials object."""
        credentials = PeerTubeCredentials(
            server_url="https://peertube.example.com",
            username="testuser",
            password="testpass"
        )
        
        self.assertEqual(credentials.server_url, "https://peertube.example.com")
        self.assertEqual(credentials.username, "testuser")
        self.assertEqual(credentials.password, "testpass")
        self.assertIsNone(credentials.client_id)
        self.assertIsNone(credentials.client_secret)
        self.assertIsNone(credentials.access_token)
        self.assertIsNone(credentials.refresh_token)
    
    def test_credentials_validation(self):
        """Test credentials validation."""
        # Valid credentials
        credentials = PeerTubeCredentials(
            server_url="https://peertube.example.com",
            username="testuser",
            password="testpass"
        )
        self.assertTrue(credentials.is_valid())
        
        # Missing server URL
        with self.assertRaises(ValueError):
            PeerTubeCredentials(
                server_url="",
                username="testuser",
                password="testpass"
            )
        
        # Missing username
        with self.assertRaises(ValueError):
            PeerTubeCredentials(
                server_url="https://peertube.example.com",
                username="",
                password="testpass"
            )
        
        # Missing password
        with self.assertRaises(ValueError):
            PeerTubeCredentials(
                server_url="https://peertube.example.com",
                username="testuser",
                password=""
            )


class TestPeerTubeAuth(unittest.TestCase):
    """Test PeerTubeAuth class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.credentials = PeerTubeCredentials(
            server_url="https://peertube.example.com",
            username="testuser",
            password="testpass"
        )
        self.auth = PeerTubeAuth(self.credentials)
    
    @patch.object(PeerTubeAuth, '_make_request')
    def test_get_client_credentials_success(self, mock_request):
        """Test successful client credentials retrieval."""
        # Mock successful response
        mock_response = Mock()
        mock_response.json.return_value = {
            "client_id": "test_client_id",
            "client_secret": "test_client_secret"
        }
        mock_request.return_value = mock_response

        client_id, client_secret = self.auth.get_client_credentials()

        self.assertEqual(client_id, "test_client_id")
        self.assertEqual(client_secret, "test_client_secret")
        mock_request.assert_called_once_with('GET', '/api/v1/oauth-clients/local')
    
    @patch.object(PeerTubeAuth, '_make_request')
    def test_get_client_credentials_failure(self, mock_request):
        """Test client credentials retrieval failure."""
        # Mock failed response
        mock_request.side_effect = requests.exceptions.HTTPError("404 Not Found")

        with self.assertRaises(requests.exceptions.HTTPError):
            self.auth.get_client_credentials()
    
    @patch.object(PeerTubeAuth, '_make_request')
    def test_get_access_token_success(self, mock_request):
        """Test successful access token retrieval."""
        # Set up credentials with client info
        self.credentials.client_id = "test_client_id"
        self.credentials.client_secret = "test_client_secret"

        # Mock successful response
        mock_response = Mock()
        mock_response.json.return_value = {
            "access_token": "test_access_token",
            "refresh_token": "test_refresh_token",
            "token_type": "Bearer",
            "expires_in": 3600
        }
        mock_request.return_value = mock_response

        access_token = self.auth.get_access_token()

        self.assertEqual(access_token, "test_access_token")
        self.assertEqual(self.credentials.access_token, "test_access_token")
        self.assertEqual(self.credentials.refresh_token, "test_refresh_token")

        # Verify the request was made correctly
        mock_request.assert_called_once_with('POST', '/api/v1/users/token', data={
            'client_id': 'test_client_id',
            'client_secret': 'test_client_secret',
            'grant_type': 'password',
            'response_type': 'code',
            'username': 'testuser',
            'password': 'testpass'
        })
    
    @patch.object(PeerTubeAuth, '_make_request')
    def test_get_access_token_failure(self, mock_request):
        """Test access token retrieval failure."""
        # Set up credentials with client info
        self.credentials.client_id = "test_client_id"
        self.credentials.client_secret = "test_client_secret"

        # Mock failed response
        mock_request.side_effect = requests.exceptions.HTTPError("401 Unauthorized")

        with self.assertRaises(requests.exceptions.HTTPError):
            self.auth.get_access_token()
    
    @patch.object(PeerTubeAuth, '_make_request')
    def test_refresh_access_token_success(self, mock_request):
        """Test successful access token refresh."""
        # Set up credentials with refresh token
        self.credentials.client_id = "test_client_id"
        self.credentials.client_secret = "test_client_secret"
        self.credentials.refresh_token = "test_refresh_token"

        # Mock successful response
        mock_response = Mock()
        mock_response.json.return_value = {
            "access_token": "new_access_token",
            "refresh_token": "new_refresh_token",
            "token_type": "Bearer",
            "expires_in": 3600
        }
        mock_request.return_value = mock_response

        new_token = self.auth.refresh_access_token()

        self.assertEqual(new_token, "new_access_token")
        self.assertEqual(self.credentials.access_token, "new_access_token")
        self.assertEqual(self.credentials.refresh_token, "new_refresh_token")
    
    @patch.object(PeerTubeAuth, '_make_request')
    def test_refresh_access_token_failure(self, mock_request):
        """Test access token refresh failure."""
        # Set up credentials with refresh token
        self.credentials.client_id = "test_client_id"
        self.credentials.client_secret = "test_client_secret"
        self.credentials.refresh_token = "test_refresh_token"

        # Mock failed response - this should fall back to get_access_token
        mock_request.side_effect = requests.exceptions.HTTPError("401 Unauthorized")

        # Since refresh fails, it should call get_access_token which will also fail
        with self.assertRaises(requests.exceptions.HTTPError):
            self.auth.refresh_access_token()
    
    @patch.object(PeerTubeAuth, 'get_access_token')
    @patch.object(PeerTubeAuth, 'get_client_credentials')
    def test_authenticate_full_flow(self, mock_get_client, mock_get_token):
        """Test full authentication flow."""
        # Mock client credentials with side effect to update credentials
        def mock_get_client_side_effect():
            self.credentials.client_id = "test_client_id"
            self.credentials.client_secret = "test_client_secret"
            return ("test_client_id", "test_client_secret")

        def mock_get_token_side_effect():
            self.credentials.access_token = "test_access_token"
            return "test_access_token"

        mock_get_client.side_effect = mock_get_client_side_effect
        mock_get_token.side_effect = mock_get_token_side_effect

        result = self.auth.authenticate()

        self.assertTrue(result)
        self.assertEqual(self.credentials.client_id, "test_client_id")
        self.assertEqual(self.credentials.client_secret, "test_client_secret")
        self.assertEqual(self.credentials.access_token, "test_access_token")

        mock_get_client.assert_called_once()
        mock_get_token.assert_called_once()
    
    @patch.object(PeerTubeAuth, 'get_client_credentials')
    def test_authenticate_client_failure(self, mock_get_client):
        """Test authentication failure at client credentials step."""
        mock_get_client.side_effect = requests.exceptions.HTTPError("Failed to get client credentials")
        
        result = self.auth.authenticate()
        
        self.assertFalse(result)
        mock_get_client.assert_called_once()
    
    @patch.object(PeerTubeAuth, 'get_access_token')
    @patch.object(PeerTubeAuth, 'get_client_credentials')
    def test_authenticate_token_failure(self, mock_get_client, mock_get_token):
        """Test authentication failure at access token step."""
        mock_get_client.return_value = ("test_client_id", "test_client_secret")
        mock_get_token.side_effect = requests.exceptions.HTTPError("Failed to get access token")
        
        result = self.auth.authenticate()
        
        self.assertFalse(result)
        mock_get_client.assert_called_once()
        mock_get_token.assert_called_once()
    
    @patch.object(PeerTubeAuth, 'get_valid_token')
    def test_get_authenticated_session(self, mock_get_valid_token):
        """Test getting authenticated session."""
        # Mock get_valid_token to return a test token
        mock_get_valid_token.return_value = "test_access_token"

        session = self.auth.get_authenticated_session()

        self.assertIsInstance(session, requests.Session)
        self.assertEqual(
            session.headers['Authorization'],
            'Bearer test_access_token'
        )
        mock_get_valid_token.assert_called_once()
    
    @patch.object(PeerTubeAuth, 'get_valid_token')
    def test_get_authenticated_session_no_token(self, mock_get_valid_token):
        """Test getting authenticated session when token retrieval fails."""
        mock_get_valid_token.side_effect = requests.exceptions.HTTPError("Failed to get token")

        with self.assertRaises(requests.exceptions.HTTPError):
            self.auth.get_authenticated_session()
    
    @patch.object(PeerTubeAuth, 'get_authenticated_session')
    def test_test_authentication_success(self, mock_get_session):
        """Test successful authentication test."""
        # Mock authenticated session
        mock_session = Mock()
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = {"username": "testuser"}
        mock_session.get.return_value = mock_response
        mock_get_session.return_value = mock_session

        result = self.auth.test_authentication()

        self.assertTrue(result)
        mock_get_session.assert_called_once()
        mock_session.get.assert_called_once_with("https://peertube.example.com/api/v1/users/me")
    
    @patch.object(PeerTubeAuth, 'get_authenticated_session')
    def test_test_authentication_failure(self, mock_get_session):
        """Test authentication test failure."""
        # Mock authenticated session that fails
        mock_session = Mock()
        mock_session.get.side_effect = requests.exceptions.HTTPError("401 Unauthorized")
        mock_get_session.return_value = mock_session

        result = self.auth.test_authentication()

        self.assertFalse(result)


if __name__ == "__main__":
    unittest.main()
