"""Video upload functionality for PeerTube."""

import json
import logging
import os
import time
from pathlib import Path
from typing import Dict, Optional, Callable
from urllib.parse import urljoin

import requests
from pydantic import BaseModel, Field
from tqdm import tqdm

from .auth import PeerTubeAuth
from .video_scanner import VideoMetadata


logger = logging.getLogger(__name__)


class UploadOptions(BaseModel):
    """Video upload options."""
    
    name: Optional[str] = Field(None, description="Video title")
    description: Optional[str] = Field(None, description="Video description")
    category: Optional[int] = Field(None, description="Video category ID")
    licence: Optional[int] = Field(None, description="Video licence ID")
    language: Optional[str] = Field(None, description="Video language code")
    privacy: int = Field(1, description="Privacy setting (1=Public, 2=Unlisted, 3=Private)")
    tags: Optional[list] = Field(None, description="Video tags")
    channel_id: Optional[int] = Field(None, description="Channel ID")
    support: Optional[str] = Field(None, description="Support text")
    nsfw: bool = Field(False, description="NSFW flag")
    wait_transcoding: bool = Field(True, description="Wait for transcoding")
    comments_enabled: bool = Field(True, description="Enable comments")
    download_enabled: bool = Field(True, description="Enable download")
    
    def to_upload_data(self) -> Dict:
        """Convert to upload data format."""
        data = {}
        
        if self.name:
            data['name'] = self.name
        if self.description:
            data['description'] = self.description
        if self.category is not None:
            data['category'] = self.category
        if self.licence is not None:
            data['licence'] = self.licence
        if self.language:
            data['language'] = self.language
        if self.privacy is not None:
            data['privacy'] = self.privacy
        if self.tags:
            data['tags'] = self.tags
        if self.channel_id is not None:
            data['channelId'] = self.channel_id
        if self.support:
            data['support'] = self.support
        
        data['nsfw'] = self.nsfw
        data['waitTranscoding'] = self.wait_transcoding
        data['commentsEnabled'] = self.comments_enabled
        data['downloadEnabled'] = self.download_enabled
        
        return data


class UploadProgress:
    """Track upload progress."""
    
    def __init__(self, total_size: int, filename: str):
        self.total_size = total_size
        self.filename = filename
        self.uploaded_size = 0
        self.start_time = time.time()
        self.progress_bar = None
    
    def start_progress_bar(self):
        """Start progress bar."""
        self.progress_bar = tqdm(
            total=self.total_size,
            unit='B',
            unit_scale=True,
            desc=f"Uploading {self.filename}"
        )
    
    def update(self, chunk_size: int):
        """Update progress."""
        self.uploaded_size += chunk_size
        if self.progress_bar:
            self.progress_bar.update(chunk_size)
    
    def close(self):
        """Close progress bar."""
        if self.progress_bar:
            self.progress_bar.close()
    
    @property
    def percentage(self) -> float:
        """Upload percentage."""
        if self.total_size == 0:
            return 0.0
        return (self.uploaded_size / self.total_size) * 100
    
    @property
    def speed_mbps(self) -> float:
        """Upload speed in MB/s."""
        elapsed = time.time() - self.start_time
        if elapsed == 0:
            return 0.0
        return (self.uploaded_size / (1024 * 1024)) / elapsed


class PeerTubeUploader:
    """Handles video uploads to PeerTube."""
    
    def __init__(self, auth: PeerTubeAuth, chunk_size: int = 1024 * 1024):
        """Initialize uploader.
        
        Args:
            auth: PeerTube authentication handler
            chunk_size: Upload chunk size in bytes (default 1MB)
        """
        self.auth = auth
        self.chunk_size = chunk_size
        self.session = None
    
    def _get_session(self) -> requests.Session:
        """Get authenticated session."""
        if not self.session:
            self.session = self.auth.get_authenticated_session()
        return self.session
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """Make authenticated request."""
        session = self._get_session()
        url = urljoin(self.auth.credentials.server_url, endpoint)
        
        try:
            response = session.request(method, url, **kwargs)
            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            logger.error(f"Request failed: {e}")
            raise
    
    def get_upload_categories(self) -> Dict:
        """Get available video categories."""
        response = self._make_request('GET', '/api/v1/videos/categories')
        return response.json()
    
    def get_upload_licences(self) -> Dict:
        """Get available video licences."""
        response = self._make_request('GET', '/api/v1/videos/licences')
        return response.json()
    
    def get_upload_languages(self) -> Dict:
        """Get available video languages."""
        response = self._make_request('GET', '/api/v1/videos/languages')
        return response.json()
    
    def get_user_channels(self) -> list:
        """Get user's video channels."""
        response = self._make_request('GET', '/api/v1/users/me')
        user_data = response.json()
        
        # Get channels for this user
        account_name = user_data['account']['name']
        response = self._make_request('GET', f'/api/v1/accounts/{account_name}/video-channels')
        return response.json()['data']
    
    def upload_video_simple(self, video_metadata: VideoMetadata, 
                           options: UploadOptions,
                           progress_callback: Optional[Callable] = None) -> Dict:
        """Upload video using simple upload (non-resumable).
        
        Args:
            video_metadata: Video file metadata
            options: Upload options
            progress_callback: Optional progress callback function
            
        Returns:
            Upload response data
        """
        logger.info(f"Starting simple upload for {video_metadata.filename}")
        
        # Prepare upload data
        upload_data = options.to_upload_data()
        
        # Use filename as title if not provided
        if not upload_data.get('name'):
            upload_data['name'] = video_metadata.file_path.stem
        
        # Prepare files
        with open(video_metadata.file_path, 'rb') as video_file:
            files = {'videofile': (video_metadata.filename, video_file, video_metadata.mime_type)}
            
            # Create progress tracker
            progress = UploadProgress(video_metadata.size_bytes, video_metadata.filename)
            if progress_callback:
                progress_callback(progress)
            else:
                progress.start_progress_bar()
            
            try:
                # Upload video
                response = self._make_request(
                    'POST', 
                    '/api/v1/videos/upload',
                    data=upload_data,
                    files=files
                )
                
                progress.uploaded_size = video_metadata.size_bytes
                progress.update(0)  # Update to 100%
                
                upload_result = response.json()
                logger.info(f"Successfully uploaded {video_metadata.filename}")
                
                return upload_result
                
            finally:
                progress.close()
    
    def init_resumable_upload(self, video_metadata: VideoMetadata,
                             options: UploadOptions) -> Dict:
        """Initialize resumable upload.
        
        Returns:
            Upload session data including upload URL
        """
        logger.info(f"Initializing resumable upload for {video_metadata.filename}")
        
        upload_data = options.to_upload_data()
        
        # Use filename as title if not provided
        if not upload_data.get('name'):
            upload_data['name'] = video_metadata.file_path.stem
        
        # Add file metadata
        upload_data['filename'] = video_metadata.filename
        upload_data['size'] = video_metadata.size_bytes
        
        response = self._make_request(
            'POST',
            '/api/v1/videos/upload-resumable',
            json=upload_data
        )
        
        # Get upload URL from Location header
        upload_url = response.headers.get('Location')
        if not upload_url:
            raise ValueError("No upload URL returned from server")
        
        return {
            'upload_url': upload_url,
            'session_id': upload_url.split('/')[-1]
        }
    
    def upload_chunk(self, upload_url: str, chunk_data: bytes, 
                    chunk_start: int, total_size: int) -> bool:
        """Upload a single chunk.
        
        Args:
            upload_url: Upload URL from init_resumable_upload
            chunk_data: Chunk data to upload
            chunk_start: Starting byte position
            total_size: Total file size
            
        Returns:
            True if chunk uploaded successfully
        """
        chunk_end = chunk_start + len(chunk_data) - 1
        
        headers = {
            'Content-Range': f'bytes {chunk_start}-{chunk_end}/{total_size}',
            'Content-Type': 'application/octet-stream'
        }
        
        session = self._get_session()
        
        try:
            response = session.put(upload_url, data=chunk_data, headers=headers)
            
            # 200 means chunk uploaded, 201 means upload complete
            if response.status_code in [200, 201]:
                return True
            else:
                logger.error(f"Chunk upload failed with status {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Chunk upload failed: {e}")
            return False

    def upload_video_resumable(self, video_metadata: VideoMetadata,
                              options: UploadOptions,
                              progress_callback: Optional[Callable] = None) -> Dict:
        """Upload video using resumable upload.

        Args:
            video_metadata: Video file metadata
            options: Upload options
            progress_callback: Optional progress callback function

        Returns:
            Upload response data
        """
        logger.info(f"Starting resumable upload for {video_metadata.filename}")

        # Initialize upload session
        session_data = self.init_resumable_upload(video_metadata, options)
        upload_url = session_data['upload_url']

        # Create progress tracker
        progress = UploadProgress(video_metadata.size_bytes, video_metadata.filename)
        if progress_callback:
            progress_callback(progress)
        else:
            progress.start_progress_bar()

        try:
            with open(video_metadata.file_path, 'rb') as video_file:
                uploaded_bytes = 0

                while uploaded_bytes < video_metadata.size_bytes:
                    # Read chunk
                    chunk_data = video_file.read(self.chunk_size)
                    if not chunk_data:
                        break

                    # Upload chunk
                    success = self.upload_chunk(
                        upload_url, chunk_data, uploaded_bytes, video_metadata.size_bytes
                    )

                    if not success:
                        raise RuntimeError(f"Failed to upload chunk at position {uploaded_bytes}")

                    uploaded_bytes += len(chunk_data)
                    progress.update(len(chunk_data))

                logger.info(f"Successfully uploaded {video_metadata.filename} using resumable upload")

                # Return session info (actual video data will be available after processing)
                return {
                    'session_id': session_data['session_id'],
                    'upload_url': upload_url,
                    'uploaded_bytes': uploaded_bytes
                }

        except Exception as e:
            logger.error(f"Resumable upload failed: {e}")
            raise
        finally:
            progress.close()

    def upload_video(self, video_metadata: VideoMetadata,
                    options: UploadOptions,
                    use_resumable: bool = True,
                    progress_callback: Optional[Callable] = None) -> Dict:
        """Upload video with automatic method selection.

        Args:
            video_metadata: Video file metadata
            options: Upload options
            use_resumable: Whether to use resumable upload for large files
            progress_callback: Optional progress callback function

        Returns:
            Upload response data
        """
        # Use resumable upload for files larger than 100MB
        large_file_threshold = 100 * 1024 * 1024  # 100MB

        if use_resumable and video_metadata.size_bytes > large_file_threshold:
            return self.upload_video_resumable(video_metadata, options, progress_callback)
        else:
            return self.upload_video_simple(video_metadata, options, progress_callback)

    def cancel_resumable_upload(self, upload_url: str) -> bool:
        """Cancel a resumable upload session.

        Args:
            upload_url: Upload URL from init_resumable_upload

        Returns:
            True if cancelled successfully
        """
        try:
            session = self._get_session()
            response = session.delete(upload_url)
            return response.status_code == 204
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to cancel upload: {e}")
            return False
