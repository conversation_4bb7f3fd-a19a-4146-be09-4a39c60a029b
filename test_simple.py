#!/usr/bin/env python3
"""Simple test to verify the package works after uv migration."""

import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

try:
    # Test basic imports
    from peertube_uploader.video_scanner import VideoMetadata, VideoScanner
    from peertube_uploader.auth import PeerTubeCredentials, PeerTubeAuth
    from peertube_uploader.config import Config
    
    print("✓ All imports successful")
    
    # Test VideoMetadata creation
    test_path = Path("/test/video.mp4")
    metadata = VideoMetadata(
        file_path=test_path,
        filename="video.mp4",
        size_bytes=1024,
        duration_seconds=60.0,
        format_name="mp4",
        video_codec="h264",
        audio_codec="aac",
        width=1920,
        height=1080,
        bitrate=5000000,
        frame_rate=30.0
    )
    
    print("✓ VideoMetadata creation successful")
    print(f"  - File: {metadata.filename}")
    print(f"  - Duration: {metadata.duration_seconds}s")
    print(f"  - Resolution: {metadata.width}x{metadata.height}")
    
    # Test PeerTubeCredentials creation
    credentials = PeerTubeCredentials(
        server_url="https://example.peertube.com",
        client_id="test_client",
        client_secret="test_secret"
    )
    
    print("✓ PeerTubeCredentials creation successful")
    print(f"  - Server: {credentials.server_url}")
    
    print("\n🎉 All tests passed! The uv migration was successful.")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ Test error: {e}")
    sys.exit(1)
