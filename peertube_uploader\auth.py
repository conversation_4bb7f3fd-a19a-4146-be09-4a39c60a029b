"""Authentication module for PeerTube API."""

import logging
import time
from typing import Optional, Tuple
from urllib.parse import urljoin

import requests
from pydantic import BaseModel, Field, field_validator


logger = logging.getLogger(__name__)


class PeerTubeCredentials(BaseModel):
    """PeerTube authentication credentials."""

    server_url: str = Field(..., description="PeerTube server URL")
    username: str = Field(..., description="Username")
    password: str = Field(..., description="Password")
    client_id: Optional[str] = Field(None, description="OAuth client ID")
    client_secret: Optional[str] = Field(None, description="OAuth client secret")
    access_token: Optional[str] = Field(None, description="Access token")
    refresh_token: Optional[str] = Field(None, description="Refresh token")
    token_expires_at: Optional[float] = Field(None, description="Token expiration timestamp")

    @field_validator('server_url', 'username', 'password')
    @classmethod
    def validate_required_fields(cls, v):
        """Validate that required fields are not empty."""
        if not v or not v.strip():
            raise ValueError("Field cannot be empty")
        return v

    def is_valid(self) -> bool:
        """Check if credentials are valid."""
        return bool(self.server_url and self.username and self.password)


class PeerTubeAuth:
    """Handles PeerTube OAuth2 authentication."""
    
    def __init__(self, credentials: PeerTubeCredentials):
        """Initialize with credentials."""
        self.credentials = credentials
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'PeerTube-Uploader/1.0.0',
            'Content-Type': 'application/json'
        })
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """Make HTTP request to PeerTube API."""
        url = urljoin(self.credentials.server_url, endpoint)
        
        try:
            response = self.session.request(method, url, **kwargs)
            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            logger.error(f"Request failed: {e}")
            raise
    
    def get_client_credentials(self) -> Tuple[str, str]:
        """Get OAuth client credentials from PeerTube server."""
        logger.info("Getting OAuth client credentials...")
        
        response = self._make_request('GET', '/api/v1/oauth-clients/local')
        data = response.json()
        
        client_id = data['client_id']
        client_secret = data['client_secret']
        
        self.credentials.client_id = client_id
        self.credentials.client_secret = client_secret
        
        logger.info("Successfully obtained client credentials")
        return client_id, client_secret
    
    def get_access_token(self) -> str:
        """Get access token using username/password."""
        if not self.credentials.client_id or not self.credentials.client_secret:
            self.get_client_credentials()
        
        logger.info("Getting access token...")
        
        data = {
            'client_id': self.credentials.client_id,
            'client_secret': self.credentials.client_secret,
            'grant_type': 'password',
            'response_type': 'code',
            'username': self.credentials.username,
            'password': self.credentials.password
        }
        
        # Use form data for token request
        self.session.headers.update({'Content-Type': 'application/x-www-form-urlencoded'})
        
        try:
            response = self._make_request('POST', '/api/v1/users/token', data=data)
            token_data = response.json()
            
            self.credentials.access_token = token_data['access_token']
            self.credentials.refresh_token = token_data.get('refresh_token')
            
            # Calculate expiration time
            expires_in = token_data.get('expires_in', 14400)  # Default 4 hours
            self.credentials.token_expires_at = time.time() + expires_in
            
            # Reset content type
            self.session.headers.update({'Content-Type': 'application/json'})
            
            logger.info("Successfully obtained access token")
            # Type assertion: access_token is guaranteed to be set above
            assert self.credentials.access_token is not None
            return self.credentials.access_token
            
        except requests.exceptions.RequestException as e:
            # Reset content type
            self.session.headers.update({'Content-Type': 'application/json'})
            logger.error(f"Failed to get access token: {e}")
            raise
    
    def refresh_access_token(self) -> str:
        """Refresh access token using refresh token."""
        if not self.credentials.refresh_token:
            logger.warning("No refresh token available, getting new access token")
            return self.get_access_token()
        
        logger.info("Refreshing access token...")
        
        data = {
            'client_id': self.credentials.client_id,
            'client_secret': self.credentials.client_secret,
            'grant_type': 'refresh_token',
            'refresh_token': self.credentials.refresh_token
        }
        
        # Use form data for token request
        self.session.headers.update({'Content-Type': 'application/x-www-form-urlencoded'})
        
        try:
            response = self._make_request('POST', '/api/v1/users/token', data=data)
            token_data = response.json()
            
            self.credentials.access_token = token_data['access_token']
            self.credentials.refresh_token = token_data.get('refresh_token')
            
            # Calculate expiration time
            expires_in = token_data.get('expires_in', 14400)  # Default 4 hours
            self.credentials.token_expires_at = time.time() + expires_in
            
            # Reset content type
            self.session.headers.update({'Content-Type': 'application/json'})
            
            logger.info("Successfully refreshed access token")
            # Type assertion: access_token is guaranteed to be set above
            assert self.credentials.access_token is not None
            return self.credentials.access_token
            
        except requests.exceptions.RequestException as e:
            # Reset content type
            self.session.headers.update({'Content-Type': 'application/json'})
            logger.warning(f"Failed to refresh token: {e}, getting new access token")
            return self.get_access_token()
    
    def is_token_expired(self) -> bool:
        """Check if access token is expired."""
        if not self.credentials.token_expires_at:
            return True
        
        # Add 60 second buffer
        return time.time() >= (self.credentials.token_expires_at - 60)
    
    def get_valid_token(self) -> str:
        """Get a valid access token, refreshing if necessary."""
        if not self.credentials.access_token or self.is_token_expired():
            if self.credentials.refresh_token and not self.is_token_expired():
                return self.refresh_access_token()
            else:
                return self.get_access_token()
        
        return self.credentials.access_token
    
    def get_authenticated_session(self) -> requests.Session:
        """Get requests session with authentication headers."""
        token = self.get_valid_token()
        
        session = requests.Session()
        session.headers.update({
            'Authorization': f'Bearer {token}',
            'User-Agent': 'PeerTube-Uploader/1.0.0'
        })
        
        return session
    
    def authenticate(self) -> bool:
        """Perform full authentication flow."""
        try:
            # Get client credentials if not available
            if not self.credentials.client_id or not self.credentials.client_secret:
                self.get_client_credentials()

            # Get access token
            self.get_access_token()

            return True

        except requests.exceptions.RequestException as e:
            logger.error(f"Authentication failed: {e}")
            return False

    def test_authentication(self) -> bool:
        """Test if authentication is working."""
        try:
            session = self.get_authenticated_session()
            response = session.get(urljoin(self.credentials.server_url, '/api/v1/users/me'))
            response.raise_for_status()

            user_data = response.json()
            logger.info(f"Authentication successful for user: {user_data.get('username', 'unknown')}")
            return True

        except requests.exceptions.RequestException as e:
            logger.error(f"Authentication test failed: {e}")
            return False
