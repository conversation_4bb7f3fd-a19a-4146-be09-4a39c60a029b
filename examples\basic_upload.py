#!/usr/bin/env python3
"""
Basic example of using PeerTube Uploader programmatically.

This example shows how to:
1. Set up authentication
2. Scan for videos
3. Upload videos with custom options
"""

import logging
from pathlib import Path

from peertube_uploader.auth import PeerTubeAuth, PeerTubeCredentials
from peertube_uploader.uploader import PeerTubeUploader, UploadOptions
from peertube_uploader.video_scanner import VideoScanner
from peertube_uploader.logging_config import setup_logging


def main():
    """Main example function."""
    
    # Set up logging
    setup_logging(log_level='INFO', console_output=True, colored_output=True)
    logger = logging.getLogger(__name__)
    
    # Configuration
    SERVER_URL = "https://your-peertube-server.com"
    USERNAME = "your_username"
    PASSWORD = "your_password"
    VIDEO_DIRECTORY = Path("./videos")  # Change this to your video directory
    
    try:
        # Step 1: Set up authentication
        logger.info("Setting up authentication...")
        credentials = PeerTubeCredentials(
            server_url=SERVER_URL,
            username=USERNAME,
            password=PASSWORD
        )
        
        auth = PeerTubeAuth(credentials)
        
        # Test authentication
        if not auth.test_authentication():
            logger.error("Authentication failed!")
            return
        
        logger.info("Authentication successful!")
        
        # Step 2: Initialize uploader and scanner
        uploader = PeerTubeUploader(auth)
        scanner = VideoScanner()
        
        # Step 3: Scan for videos
        logger.info(f"Scanning {VIDEO_DIRECTORY} for videos...")
        videos = scanner.scan_directory(VIDEO_DIRECTORY, recursive=True)
        
        if not videos:
            logger.warning("No videos found!")
            return
        
        logger.info(f"Found {len(videos)} videos")
        
        # Step 4: Filter videos (optional)
        # Only upload videos between 1MB and 100MB
        videos = scanner.filter_by_size(videos, min_size_mb=1.0, max_size_mb=100.0)
        
        # Only upload videos between 30 seconds and 10 minutes
        videos = scanner.filter_by_duration(videos, min_duration_seconds=30, max_duration_seconds=600)
        
        logger.info(f"After filtering: {len(videos)} videos")
        
        if not videos:
            logger.warning("No videos match the filters!")
            return
        
        # Step 5: Set up upload options
        upload_options = UploadOptions(
            category=1,  # Adjust based on your server's categories
            licence=1,   # Adjust based on your server's licences
            language="en",
            privacy=1,   # 1=Public, 2=Unlisted, 3=Private
            tags=["example", "upload", "demo"],
            description="Uploaded using PeerTube Uploader example script",
            nsfw=False,
            comments_enabled=True,
            download_enabled=True,
            wait_transcoding=True
        )
        
        # Step 6: Upload videos
        successful_uploads = 0
        failed_uploads = 0
        
        for i, video in enumerate(videos, 1):
            logger.info(f"Uploading {i}/{len(videos)}: {video.filename}")
            
            try:
                # Customize title for each video (use filename without extension)
                video_options = upload_options.copy()
                video_options.name = video.file_path.stem
                
                # Upload the video
                result = uploader.upload_video(
                    video, 
                    video_options, 
                    use_resumable=True  # Use resumable upload for large files
                )
                
                logger.info(f"Successfully uploaded: {video.filename}")
                successful_uploads += 1
                
            except Exception as e:
                logger.error(f"Failed to upload {video.filename}: {e}")
                failed_uploads += 1
                continue
        
        # Step 7: Summary
        logger.info(f"\nUpload Summary:")
        logger.info(f"Successful uploads: {successful_uploads}")
        logger.info(f"Failed uploads: {failed_uploads}")
        logger.info(f"Success rate: {(successful_uploads / len(videos) * 100):.1f}%")
        
    except Exception as e:
        logger.error(f"Error in main: {e}")
        raise


if __name__ == "__main__":
    main()
